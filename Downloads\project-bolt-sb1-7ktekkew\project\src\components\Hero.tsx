import React, { useState, useEffect } from "react";
import { ArrowDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      image:
        "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=1920",
      title: "Premium Residential Plots",
      subtitle: "Shreyas Properties",
      description:
        "Invest in a premium gated development of Bangalore's fastest growing destination. Near International Airport with excellent connectivity and modern amenities.",
      highlight: "Ready for Registration | RERA Approved",
    },
    {
      image:
        "https://images.pexels.com/photos/1475938/pexels-photo-1475938.jpeg?auto=compress&cs=tinysrgb&w=1920",
      title: "Invest Now",
      subtitle: "Near International Airport",
      description:
        "Strategic location close to Bengaluru's fast developing satellite township Nandagudi and just minutes away from Devanahalli. Excellent connectivity via State Highway 35.",
      highlight: "High Appreciation Potential | Prime Location",
    },
    {
      image:
        "https://images.pexels.com/photos/2121121/pexels-photo-2121121.jpeg?auto=compress&cs=tinysrgb&w=1920",
      title: "Ready for Registration",
      subtitle: "Residential Plots",
      description:
        "Premium gated community with swimming pool, clubhouse, sports facilities, and eco-friendly environment. Plot sizes ranging from 30x40 to 60x80 feet with DTCP approval.",
      highlight: "RERA & DTCP Approved | Premium Amenities",
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 6000);
    return () => clearInterval(timer);
  }, [slides.length]);

  const scrollToNext = () => {
    document
      .getElementById("properties")
      ?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <section id="home" className="relative h-screen overflow-hidden">
      {/* Background Slider */}
      <div className="absolute inset-0">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ duration: 1.2, ease: "easeInOut" }}
            className="absolute inset-0 bg-cover bg-center"
            style={{ backgroundImage: `url(${slides[currentSlide].image})` }}
          />
        </AnimatePresence>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-hero-gradient" />

        {/* Animated Background Elements */}
        <motion.div
          initial={{ y: 0 }}
          animate={{ y: -50 }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage:
              "radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.15) 0%, transparent 50%), radial-gradient(circle at 50% 80%, rgba(30, 41, 59, 0.1) 0%, transparent 50%)",
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen">
        <div className="max-w-6xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-8"
          >
            <motion.h1
              className="text-hero-mobile md:text-hero font-heading font-bold text-accent-50 drop-shadow-lg"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {slides[currentSlide].title}
            </motion.h1>
            <motion.h2
              className="text-heading-md md:text-heading-lg font-accent font-medium text-secondary-300 tracking-wide drop-shadow-md"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {slides[currentSlide].subtitle}
            </motion.h2>
            <motion.p
              className="text-body-lg md:text-heading-sm text-accent-100 font-body max-w-3xl mx-auto drop-shadow-sm"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              {slides[currentSlide].description}
            </motion.p>
            {slides[currentSlide].highlight && (
              <motion.div
                className="glass-effect border border-secondary-400/30 rounded-xl px-8 py-4 inline-block shadow-glow"
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: 1.0 }}
              >
                <p className="text-body font-semibold text-secondary-200">
                  {slides[currentSlide].highlight}
                </p>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`h-2 w-8 rounded-full transition-all duration-300 ${
              index === currentSlide ? "bg-secondary" : "bg-white/40"
            }`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.8 }}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <motion.button
        onClick={scrollToNext}
        className="absolute bottom-8 right-8 p-3 text-accent/70 hover:text-accent transition-colors duration-300"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        whileHover={{ scale: 1.1 }}
      >
        <ArrowDown className="h-6 w-6" />
      </motion.button>
    </section>
  );
};

export default Hero;
