import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Building,
  MapPin,
  Users,
  TrendingUp,
  Clock,
  Briefcase,
  IndianRupee,
  Rocket,
} from "lucide-react";

// Animation variants for Framer Motion
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15, delayChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 30, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};


// Helper component for stat cards
const StatCard = ({ icon: Icon, value, label, description }) => (
  <motion.div
    variants={itemVariants}
    className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-4 sm:p-6 text-center transform transition-all duration-300 hover:bg-white/20 hover:-translate-y-2 hover:shadow-2xl"
  >
    <div className="flex justify-center mb-4">
      <div className="bg-white/10 p-3 rounded-full border border-white/20">
        <Icon className="h-7 w-7 text-white" />
      </div>
    </div>
    <p className="text-3xl lg:text-4xl font-bold text-white mb-1">{value}</p>
    <p className="text-sm font-semibold text-orange-100 mb-2">{label}</p>
    <p className="text-xs text-orange-200">{description}</p>
  </motion.div>
);

// Helper component for employment hub cards
const HubCard = ({ hub }) => {
  const statusColors = {
    Operational: "bg-green-500/20 text-green-200 border-green-500/30",
    Expanding: "bg-sky-500/20 text-sky-200 border-sky-500/30",
    "Under Development": "bg-amber-500/20 text-amber-200 border-amber-500/30",
    Developing: "bg-purple-500/20 text-purple-200 border-purple-500/30",
  };

  return (
    <motion.div
      variants={itemVariants}
      className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 sm:p-8 flex flex-col transition-all duration-300 hover:bg-white/20 hover:border-white/30 hover:shadow-2xl"
    >
      <div className="flex items-start space-x-5 mb-4">
        <div className={`p-3 rounded-lg ${hub.color.replace('text-','text-light-')} border border-white/20`}>
          <hub.icon className="h-8 w-8" />
        </div>
        <div className="flex-1">
          <div className="flex justify-between items-start mb-1">
            <h3 className="text-xl font-bold text-white">{hub.name}</h3>
            <span
              className={`text-xs font-medium px-3 py-1 rounded-full whitespace-nowrap ${
                statusColors[hub.status] || "bg-white/20 text-orange-100"
              }`}
            >
              {hub.status}
            </span>
          </div>
          <p className="text-sm text-orange-100">{hub.description}</p>
        </div>
      </div>

      <div className="mt-auto pt-4 grid grid-cols-2 gap-x-6 gap-y-4 border-t border-white/20">
        <div className="flex items-center space-x-2">
          <IndianRupee className="w-5 h-5 text-orange-200/80" />
          <div>
            <p className="text-xs text-orange-200">Investment</p>
            <p className="text-base font-semibold text-white">{hub.investment}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="w-5 h-5 text-orange-200/80" />
          <div>
            <p className="text-xs text-orange-200">Distance</p>
            <p className="text-base font-semibold text-white">{hub.distance}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Users className="w-5 h-5 text-orange-200/80" />
          <div>
            <p className="text-xs text-orange-200">Employment</p>
            <p className="text-base font-semibold text-white">{hub.employees}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Briefcase className="w-5 h-5 text-orange-200/80" />
          <div>
            <p className="text-xs text-orange-200">Sector</p>
            <p className="text-base font-semibold text-white">{hub.type}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Main Component
const EmploymentHub = () => {
  // Intersection observer for scroll animations
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Data for the component
  const employmentHubs = [
    {
      name: "Foxconn iPhone Campus",
      investment: "₹22,000 Cr",
      distance: "15 mins",
      employees: "50,000+",
      type: "Manufacturing",
      description: "World's largest iPhone facility outside China.",
      icon: Building,
      color: "bg-blue-500/20 text-blue-300",
      status: "Under Development",
    },
    {
      name: "SAP Labs New Campus",
      investment: "₹1,500 Cr",
      distance: "18 mins",
      employees: "15,000+",
      type: "IT Services",
      description: "Major software development and innovation center.",
      icon: Building,
      color: "bg-green-500/20 text-green-300",
      status: "Expanding",
    },
    {
      name: "Infosys Facility",
      investment: "₹700 Cr",
      distance: "20 mins",
      employees: "25,000+",
      type: "IT Services",
      description: "Large-scale IT services and development center.",
      icon: Building,
      color: "bg-purple-500/20 text-purple-300",
      status: "Operational",
    },
    {
      name: "Aerospace Park",
      investment: "₹5,000 Cr",
      distance: "12 mins",
      employees: "30,000+",
      type: "Aerospace",
      description: "India's largest aerospace & defense hub.",
      icon: Rocket,
      color: "bg-orange-500/20 text-orange-300",
      status: "Developing",
    },
  ];

  const investmentStats = [
    {
      icon: IndianRupee,
      value: "₹29,200+",
      label: "Crores Investment",
      description: "Total industrial investment in the region",
    },
    {
      icon: Users,
      value: "1,20,000+",
      label: "Employment",
      description: "Direct and indirect job creation",
    },
    {
      icon: TrendingUp,
      value: "25%",
      label: "Annual Growth",
      description: "Property appreciation rate",
    },
    {
      icon: Clock,
      value: "20 mins",
      label: "Average Commute",
      description: "To major employment centers",
    },
  ];

  return (
    <section
      id="employment"
      className="py-16 sm:py-24 bg-gradient-to-br from-orange-600 to-red-600 text-white font-sans"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Header Section */}
          <motion.div variants={itemVariants} className="text-center space-y-4">
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              Major Employment Hubs
            </h2>
            <p className="text-lg text-orange-100 max-w-3xl mx-auto">
              Devanahalli is at the center of North Bengaluru's industrial
              revolution, with major corporations investing over{" "}
              <span className="font-bold text-white">₹29,000 Crores</span> in
              world-class facilities, creating unprecedented employment and
              investment opportunities.
            </p>
          </motion.div>

          {/* Investment Statistics Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6">
            {investmentStats.map((stat, index) => (
              <StatCard key={index} {...stat} />
            ))}
          </div>

          {/* Employment Hubs Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {employmentHubs.map((hub, index) => (
              <HubCard key={index} hub={hub} />
            ))}
          </div>

        </motion.div>
      </div>
    </section>
  );
};

export default EmploymentHub;
