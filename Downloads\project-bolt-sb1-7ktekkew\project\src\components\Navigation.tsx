import React, { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { name: "Home", href: "#home" },
    { name: "Properties", href: "#properties" },
    { name: "Location", href: "#location" },
    { name: "About", href: "#about" },
    { name: "Amenities", href: "#amenities" },
    { name: "Testimonials", href: "#testimonials" },
    { name: "Contact", href: "#contact" },
  ];

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-accent-50/95 backdrop-blur-md shadow-luxury border-b border-accent-200/30"
          : "bg-gradient-to-b from-primary-900/30 via-primary-800/20 to-transparent backdrop-blur-sm"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <motion.div
            className="flex-shrink-0"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <h1
              className={`text-heading-lg font-heading font-bold tracking-tight transition-colors duration-300 ${
                isScrolled
                  ? "text-primary-800"
                  : "text-accent-50 drop-shadow-lg"
              }`}
            >
              Shreyas Properties
            </h1>
            <p
              className={`text-caption font-accent font-medium tracking-wide transition-colors duration-300 ${
                isScrolled
                  ? "text-secondary-600"
                  : "text-secondary-300 drop-shadow-md"
              }`}
            >
              Premier Luxury Real Estate
            </p>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              {navItems.map((item) => (
                <motion.button
                  key={item.name}
                  onClick={() => scrollToSection(item.href)}
                  className={`transition-colors duration-200 font-accent font-medium relative group tracking-wide ${
                    isScrolled
                      ? "text-primary-700 hover:text-secondary-600"
                      : "text-accent-50 hover:text-secondary-300 drop-shadow-md"
                  }`}
                  whileHover={{ y: -2 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {item.name}
                  <span
                    className={`absolute -bottom-1 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full shadow-glow ${
                      isScrolled
                        ? "bg-gradient-to-r from-secondary-600 to-secondary-500"
                        : "bg-gradient-to-r from-secondary-300 to-secondary-400"
                    }`}
                  ></span>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`transition-colors duration-200 p-2 ${
                isScrolled
                  ? "text-primary-700 hover:text-secondary-600"
                  : "text-accent-50 hover:text-secondary-300 drop-shadow-md"
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="md:hidden bg-accent-50/98 backdrop-blur-md border-t border-secondary-200/30 shadow-luxury"
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {navItems.map((item) => (
                <motion.button
                  key={item.name}
                  onClick={() => scrollToSection(item.href)}
                  className="text-primary-700 hover:text-secondary-600 block px-3 py-2 text-base font-accent font-medium w-full text-left rounded-lg hover:bg-secondary-50/50 transition-all duration-200"
                  whileHover={{ x: 10 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {item.name}
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navigation;
